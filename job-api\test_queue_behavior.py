#!/usr/bin/env python3
"""
Test script to verify the new job queue threshold behavior
"""

import asyncio
import httpx
import json
from datetime import datetime

async def test_job_queue_behavior():
    """Test the new job queue behavior with a real user"""
    
    # Test user ID (you can replace with a real user ID from your database)
    test_user_id = "49547f87-b835-4fdd-84c9-5ec2dc8739d2"
    
    # API endpoints to test
    base_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://**********:8000"
    ]
    
    print("🧪 Testing Job Queue Threshold Behavior")
    print("=" * 50)
    
    for base_url in base_urls:
        try:
            print(f"\n🔗 Testing connection to: {base_url}")
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test 1: Get initial queue stats
                print("\n📊 Step 1: Getting initial queue stats...")
                stats_response = await client.get(f"{base_url}/jobs/queue-stats/{test_user_id}")
                
                if stats_response.status_code == 200:
                    stats = stats_response.json()
                    print(f"   Available jobs: {stats.get('available_jobs_count', 0)}")
                    print(f"   Swiped jobs: {stats.get('swiped_jobs_count', 0)}")
                    print(f"   Last fetched: {stats.get('last_fetched', 'Never')}")
                    
                    initial_job_count = stats.get('available_jobs_count', 0)
                    
                    # Test 2: Request jobs (should respect threshold)
                    print(f"\n🎯 Step 2: Requesting jobs (current count: {initial_job_count})...")
                    jobs_response = await client.get(f"{base_url}/jobs/for-user/{test_user_id}?limit=5")
                    
                    if jobs_response.status_code == 200:
                        jobs_data = jobs_response.json()
                        returned_jobs = len(jobs_data.get('jobs', []))
                        queue_stats = jobs_data.get('queue_stats', {})
                        
                        print(f"   ✅ Returned {returned_jobs} jobs")
                        print(f"   📈 Queue stats after request:")
                        print(f"      Available: {queue_stats.get('available_jobs_count', 0)}")
                        print(f"      Status: {queue_stats.get('queue_status', 'Unknown')}")
                        
                        # Test 3: Check if threshold behavior is working
                        if initial_job_count >= 10:
                            print(f"\n✅ THRESHOLD TEST: User had {initial_job_count} jobs (>= 10)")
                            print("   Expected: No new job fetching should occur")
                            print("   This should prevent unnecessary API calls")
                        else:
                            print(f"\n⚠️  THRESHOLD TEST: User had {initial_job_count} jobs (< 10)")
                            print("   Expected: New jobs should be fetched to reach threshold")
                        
                        # Test 4: Warm queue manually to see behavior
                        print(f"\n🔥 Step 3: Testing manual queue warming...")
                        warm_response = await client.post(f"{base_url}/jobs/warm-queue/{test_user_id}")
                        
                        if warm_response.status_code == 200:
                            warm_data = warm_response.json()
                            final_stats = warm_data.get('queue_stats', {})
                            final_count = final_stats.get('available_jobs_count', 0)
                            
                            print(f"   ✅ Queue warming completed")
                            print(f"   📈 Final job count: {final_count}")
                            
                            if final_count >= 10:
                                print(f"   ✅ SUCCESS: Queue now has sufficient jobs ({final_count} >= 10)")
                            else:
                                print(f"   ⚠️  Queue still below threshold ({final_count} < 10)")
                        else:
                            print(f"   ❌ Queue warming failed: {warm_response.status_code}")
                    else:
                        print(f"   ❌ Jobs request failed: {jobs_response.status_code}")
                else:
                    print(f"   ❌ Stats request failed: {stats_response.status_code}")
                    
            print(f"\n✅ Successfully tested {base_url}")
            return  # Exit after first successful connection
            
        except Exception as e:
            print(f"   ❌ Failed to connect to {base_url}: {str(e)}")
            continue
    
    print("\n❌ All API endpoints failed to respond")

def print_summary():
    """Print summary of the new behavior"""
    print("\n" + "=" * 60)
    print("📋 NEW JOB QUEUE BEHAVIOR SUMMARY")
    print("=" * 60)
    print("✅ IMPLEMENTED FEATURES:")
    print("   • Minimum job threshold: 10 jobs")
    print("   • Skip fetching when user has >= 10 jobs")
    print("   • Only fetch new jobs when queue drops below 10")
    print("   • Prevents unnecessary API calls for users with sufficient jobs")
    print("   • Configurable threshold via MIN_JOBS_THRESHOLD constant")
    print("\n🎯 BENEFITS:")
    print("   • Reduces API usage and costs")
    print("   • Improves response times for users with sufficient jobs")
    print("   • Prevents redundant job fetching on repeated logins")
    print("   • Maintains good user experience with adequate job supply")
    print("\n🔧 CONFIGURATION:")
    print("   • Threshold: 10 jobs (configurable in job_queue_manager.py)")
    print("   • Applies to both automatic fetching and manual warming")
    print("   • Logs clearly indicate when fetching is skipped")

if __name__ == "__main__":
    print_summary()
    print("\n🚀 Starting API tests...")
    asyncio.run(test_job_queue_behavior())
