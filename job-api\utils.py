"""
Utility functions for the job API
"""

import hashlib
import json
import time
import uuid
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import logging
import asyncio
from functools import wraps

logger = logging.getLogger(__name__)


def generate_request_id() -> str:
    """Generate a unique request ID"""
    return str(uuid.uuid4())


# Cache key generation moved to supabase_cache.py


def sanitize_string(text: str, max_length: int = 1000) -> str:
    """Sanitize and truncate string input"""
    if not isinstance(text, str):
        return ""
    
    # Remove null bytes and control characters
    sanitized = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
    
    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length] + "..."
    
    return sanitized.strip()


def validate_email(email: str) -> bool:
    """Basic email validation"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def format_salary(salary: Union[str, int, float]) -> Optional[str]:
    """Format salary string consistently"""
    if not salary:
        return None
    
    # If it's already a formatted string, return as is
    if isinstance(salary, str):
        return salary.strip()
    
    # If it's a number, format it
    try:
        num = float(salary)
        if num >= 1000000:
            return f"${num/1000000:.1f}M"
        elif num >= 1000:
            return f"${num/1000:.0f}K"
        else:
            return f"${num:.0f}"
    except (ValueError, TypeError):
        return str(salary)


def extract_salary_range(salary_text: str) -> Dict[str, Optional[int]]:
    """Extract min and max salary from text"""
    import re
    
    if not salary_text:
        return {"min": None, "max": None}
    
    # Remove common currency symbols and normalize
    text = salary_text.lower().replace('$', '').replace(',', '')
    
    # Look for patterns like "50000-80000", "50k-80k", "50-80k"
    patterns = [
        r'(\d+)k?\s*-\s*(\d+)k',  # 50-80k or 50k-80k
        r'(\d+)\s*-\s*(\d+)',     # 50000-80000
        r'(\d+)k',                # 50k (single value)
        r'(\d+)',                 # 50000 (single value)
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            if len(match.groups()) == 2:
                min_val = int(match.group(1))
                max_val = int(match.group(2))
                
                # Handle k suffix
                if 'k' in text:
                    min_val *= 1000
                    max_val *= 1000
                
                return {"min": min_val, "max": max_val}
            else:
                val = int(match.group(1))
                if 'k' in text:
                    val *= 1000
                return {"min": val, "max": val}
    
    return {"min": None, "max": None}


def calculate_job_quality_score(job: Dict[str, Any]) -> float:
    """Calculate a quality score for a job posting (0-1 scale)"""
    score = 0.0
    max_score = 10.0
    
    # Check for required fields
    if job.get('title'):
        score += 2.0
    if job.get('company'):
        score += 2.0
    if job.get('description'):
        score += 2.0
    
    # Check description quality
    description = job.get('description', '')
    if len(description) > 100:
        score += 1.0
    if len(description) > 500:
        score += 1.0
    
    # Check for salary information
    if job.get('salary') or job.get('salary_range'):
        score += 1.0
    
    # Check for location
    if job.get('location'):
        score += 1.0
    
    return min(score / max_score, 1.0)


def timing_decorator(func):
    """Decorator to measure function execution time"""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = (time.time() - start_time) * 1000  # Convert to milliseconds
            logger.debug(f"{func.__name__} completed in {duration:.2f}ms")
            return result
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            logger.error(f"{func.__name__} failed after {duration:.2f}ms: {str(e)}")
            raise
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = (time.time() - start_time) * 1000
            logger.debug(f"{func.__name__} completed in {duration:.2f}ms")
            return result
        except Exception as e:
            duration = (time.time() - start_time) * 1000
            logger.error(f"{func.__name__} failed after {duration:.2f}ms: {str(e)}")
            raise
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


def batch_process(items: List[Any], batch_size: int = 10):
    """Process items in batches"""
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely parse JSON string"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """Safely serialize object to JSON"""
    try:
        return json.dumps(obj, default=str)
    except (TypeError, ValueError):
        return default


def normalize_location(location: str) -> str:
    """Normalize location string for consistent matching"""
    if not location:
        return ""
    
    # Convert to lowercase and remove extra spaces
    normalized = ' '.join(location.lower().split())
    
    # Common replacements
    replacements = {
        'usa': 'united states',
        'us': 'united states',
        'uk': 'united kingdom',
        'ny': 'new york',
        'ca': 'california',
        'sf': 'san francisco',
        'la': 'los angeles',
        'nyc': 'new york city'
    }
    
    for abbrev, full in replacements.items():
        normalized = normalized.replace(abbrev, full)
    
    return normalized


def is_remote_job(job: Dict[str, Any]) -> bool:
    """Check if a job is remote based on location and description"""
    location = job.get('location', '').lower()
    description = job.get('description', '').lower()
    
    remote_keywords = ['remote', 'work from home', 'telecommute', 'distributed', 'anywhere']
    
    return any(keyword in location or keyword in description for keyword in remote_keywords)
