#!/usr/bin/env python3
"""
Supabase Cache Cleanup Script
Removes expired job search cache entries from the database
"""

import asyncio
import logging
from datetime import datetime
from supabase_cache import cleanup_expired_job_cache, get_job_cache_stats

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main cleanup function"""
    try:
        logger.info("Starting Supabase cache cleanup...")
        
        # Get cache stats before cleanup
        stats_before = await get_job_cache_stats()
        logger.info(f"Cache stats before cleanup: {stats_before}")
        
        # Clean up expired entries
        deleted_count = await cleanup_expired_job_cache()
        
        # Get cache stats after cleanup
        stats_after = await get_job_cache_stats()
        logger.info(f"Cache stats after cleanup: {stats_after}")
        
        logger.info(f"✅ Cache cleanup completed. Deleted {deleted_count} expired entries.")
        
        return {
            "success": True,
            "deleted_count": deleted_count,
            "stats_before": stats_before,
            "stats_after": stats_after,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Cache cleanup failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    result = asyncio.run(main())
    print(f"Cleanup result: {result}")
