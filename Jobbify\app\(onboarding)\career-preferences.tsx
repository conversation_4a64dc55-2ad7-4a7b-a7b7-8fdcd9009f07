import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, TextInput, Alert } from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { supabase } from '@/lib/supabase';
import FontAwesome from '@expo/vector-icons/FontAwesome';

export default function CareerPreferencesOnboarding() {
  const { theme, user, refreshUserProfile } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Job type options
  const jobTypes = [
    'Full-time',
    'Part-time',
    'Contract',
    'Freelance',
    'Internship'
  ];

  // Experience level options
  const experienceLevels = [
    'entry',
    'junior',
    'mid',
    'senior',
    'lead',
    'executive'
  ];

  // Industry options
  const industries = [
    'Technology',
    'Healthcare',
    'Finance',
    'Education',
    'Marketing',
    'Sales',
    'Design',
    'Engineering',
    'Data Science',
    'Product Management',
    'Human Resources',
    'Operations',
    'Customer Service',
    'Consulting',
    'Media',
    'Non-profit',
    'Government',
    'Other'
  ];

  // Company size options
  const companySizes = [
    'startup',
    'small',
    'medium',
    'large',
    'enterprise'
  ];

  // Remote work preferences
  const remotePreferences = [
    'required',
    'preferred',
    'acceptable',
    'not_preferred'
  ];

  // Form state for comprehensive job preferences
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>(['Full-time']);
  const [experienceLevel, setExperienceLevel] = useState('');
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedCompanySizes, setSelectedCompanySizes] = useState<string[]>([]);
  const [remoteWorkPreference, setRemoteWorkPreference] = useState('preferred');
  const [preferredLocations, setPreferredLocations] = useState('');
  const [willingToRelocate, setWillingToRelocate] = useState(false);
  const [salaryMin, setSalaryMin] = useState('');
  const [salaryMax, setSalaryMax] = useState('');
  const [preferredJobTitles, setPreferredJobTitles] = useState('');
  const [requiredSkills, setRequiredSkills] = useState('');
  const [preferredSkills, setPreferredSkills] = useState('');
  
  // Toggle selection in array
  const toggleSelection = (item: string, currentSelections: string[], setSelections: React.Dispatch<React.SetStateAction<string[]>>, maxSelections = 5) => {
    if (currentSelections.includes(item)) {
      setSelections(currentSelections.filter(i => i !== item));
    } else {
      if (currentSelections.length < maxSelections) {
        setSelections([...currentSelections, item]);
      } else {
        Alert.alert('Selection limit', `You can only select up to ${maxSelections} options.`);
      }
    }
  };

  // Save preferences to Supabase
  const savePreferences = async () => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    if (selectedJobTypes.length === 0) {
      setError('Please select at least one job type');
      return;
    }

    if (!experienceLevel) {
      setError('Please select your experience level');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Parse locations and skills
      const locationsArray = preferredLocations.split(',').map(loc => loc.trim()).filter(loc => loc.length > 0);
      const jobTitlesArray = preferredJobTitles.split(',').map(title => title.trim()).filter(title => title.length > 0);
      const requiredSkillsArray = requiredSkills.split(',').map(skill => skill.trim()).filter(skill => skill.length > 0);
      const preferredSkillsArray = preferredSkills.split(',').map(skill => skill.trim()).filter(skill => skill.length > 0);

      // Create user profile first
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      const userEmail = currentUser?.email;

      // Create or update profile
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          name: user.name || 'Job Seeker',
          email: userEmail,
          user_type: 'job_seeker'
        });

      if (profileError) throw profileError;

      // Save comprehensive job preferences to new table
      const { error: preferencesError } = await supabase
        .from('user_job_preferences')
        .upsert({
          user_id: user.id,
          preferred_job_types: selectedJobTypes,
          experience_level: experienceLevel,
          preferred_industries: selectedIndustries,
          preferred_company_sizes: selectedCompanySizes,
          remote_work_preference: remoteWorkPreference,
          preferred_locations: locationsArray,
          willing_to_relocate: willingToRelocate,
          min_salary: salaryMin ? parseInt(salaryMin, 10) : null,
          max_salary: salaryMax ? parseInt(salaryMax, 10) : null,
          preferred_job_titles: jobTitlesArray,
          required_skills: requiredSkillsArray,
          preferred_skills: preferredSkillsArray
        });

      if (preferencesError) throw preferencesError;

      // Mark onboarding as complete
      const { error: onboardingError } = await supabase
        .from('profiles')
        .update({ onboarding_completed: true })
        .eq('id', user.id);

      if (onboardingError) {
        console.error('Error marking onboarding complete:', onboardingError);
        // Don't fail the whole process for this
      } else {
        // Refresh user profile to update onboarding status
        await refreshUserProfile();
      }

      // Navigate to home after successful save
      router.replace('/(tabs)/home');

    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('Failed to save your preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: themeColors.background }]}
      contentContainerStyle={{ paddingBottom: 40 }}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>Let's personalize your job search</Text>
        <Text style={[styles.headerSubtitle, { color: themeColors.textSecondary }]}>
          Help us understand what you're looking for so we can find the right opportunities for you
        </Text>
      </View>
      
      {/* Error message */}
      {error ? (
        <Text style={styles.error}>{error}</Text>
      ) : null}

      {/* Job Types Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What type of employment are you looking for?</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Select all that apply</Text>

        <View style={styles.optionsGrid}>
          {jobTypes.map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.optionButton,
                selectedJobTypes.includes(type) && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => toggleSelection(type, selectedJobTypes, setSelectedJobTypes)}
            >
              <Text
                style={[
                  styles.optionText,
                  selectedJobTypes.includes(type) && { color: '#fff' },
                  !selectedJobTypes.includes(type) && { color: themeColors.text }
                ]}
              >
                {type}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Experience Level Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What's your experience level?</Text>

        <View style={styles.optionsRow}>
          {experienceLevels.map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.pillButton,
                experienceLevel === level && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setExperienceLevel(level)}
            >
              <Text
                style={[
                  styles.pillText,
                  experienceLevel === level && { color: '#fff' },
                  experienceLevel !== level && { color: themeColors.text }
                ]}
              >
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Industries Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Which industries interest you?</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Select up to 5 options</Text>

        <View style={styles.optionsGrid}>
          {industries.map((industry) => (
            <TouchableOpacity
              key={industry}
              style={[
                styles.optionButton,
                selectedIndustries.includes(industry) && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => toggleSelection(industry, selectedIndustries, setSelectedIndustries)}
            >
              <Text
                style={[
                  styles.optionText,
                  selectedIndustries.includes(industry) && { color: '#fff' },
                  !selectedIndustries.includes(industry) && { color: themeColors.text }
                ]}
              >
                {industry}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Company Size Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What company sizes do you prefer?</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Select all that apply</Text>

        <View style={styles.optionsRow}>
          {companySizes.map((size) => (
            <TouchableOpacity
              key={size}
              style={[
                styles.pillButton,
                selectedCompanySizes.includes(size) && { backgroundColor: themeColors.primary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => toggleSelection(size, selectedCompanySizes, setSelectedCompanySizes)}
            >
              <Text
                style={[
                  styles.pillText,
                  selectedCompanySizes.includes(size) && { color: '#fff' },
                  !selectedCompanySizes.includes(size) && { color: themeColors.text }
                ]}
              >
                {size.charAt(0).toUpperCase() + size.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Remote Work Preference Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What's your remote work preference?</Text>

        <View style={styles.verticalOptions}>
          {remotePreferences.map((pref) => (
            <TouchableOpacity
              key={pref}
              style={[
                styles.verticalOption,
                remoteWorkPreference === pref && { backgroundColor: themeColors.backgroundSecondary },
                { borderColor: themeColors.border }
              ]}
              onPress={() => setRemoteWorkPreference(pref)}
            >
              <View style={styles.radioContainer}>
                <View
                  style={[
                    styles.radioOuter,
                    { borderColor: remoteWorkPreference === pref ? themeColors.primary : themeColors.border }
                  ]}
                >
                  {remoteWorkPreference === pref && (
                    <View style={[styles.radioInner, { backgroundColor: themeColors.primary }]} />
                  )}
                </View>
              </View>
              <Text style={[styles.verticalOptionText, { color: themeColors.text }]}>
                {pref.charAt(0).toUpperCase() + pref.slice(1).replace('_', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Location Preferences Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Preferred Locations</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Enter cities/regions separated by commas</Text>

        <TextInput
          style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
          value={preferredLocations}
          onChangeText={setPreferredLocations}
          placeholder="e.g., San Francisco, New York, Remote"
          placeholderTextColor={themeColors.textSecondary}
          multiline
        />

        <View style={styles.switchContainer}>
          <Text style={[styles.switchLabel, { color: themeColors.text }]}>Willing to relocate</Text>
          <Switch
            value={willingToRelocate}
            onValueChange={setWillingToRelocate}
            trackColor={{ false: themeColors.border, true: themeColors.primary }}
            thumbColor={willingToRelocate ? '#fff' : themeColors.textSecondary}
          />
        </View>
      </View>

      {/* Job Titles Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Preferred Job Titles</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Enter job titles separated by commas</Text>

        <TextInput
          style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
          value={preferredJobTitles}
          onChangeText={setPreferredJobTitles}
          placeholder="e.g., Software Engineer, Frontend Developer, Full Stack Developer"
          placeholderTextColor={themeColors.textSecondary}
          multiline
        />
      </View>

      {/* Skills Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Required Skills</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Skills you must have for the job</Text>

        <TextInput
          style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
          value={requiredSkills}
          onChangeText={setRequiredSkills}
          placeholder="e.g., JavaScript, React, Node.js"
          placeholderTextColor={themeColors.textSecondary}
          multiline
        />
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Preferred Skills</Text>
        <Text style={[styles.sectionSubtitle, { color: themeColors.textSecondary }]}>Skills you'd like to use or learn</Text>

        <TextInput
          style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
          value={preferredSkills}
          onChangeText={setPreferredSkills}
          placeholder="e.g., TypeScript, GraphQL, AWS"
          placeholderTextColor={themeColors.textSecondary}
          multiline
        />
      </View>
      
      {/* Salary Expectations Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>What are your salary expectations?</Text>
        
        <View style={styles.salaryContainer}>
          <View style={styles.salaryField}>
            <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>Minimum ($)</Text>
            <TextInput
              style={[styles.salaryInput, { borderColor: themeColors.border, color: themeColors.text }]}
              keyboardType="numeric"
              value={salaryMin}
              onChangeText={setSalaryMin}
              placeholder="Minimum"
              placeholderTextColor={themeColors.textSecondary}
            />
          </View>
          
          <View style={styles.salaryField}>
            <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>Maximum ($)</Text>
            <TextInput
              style={[styles.salaryInput, { borderColor: themeColors.border, color: themeColors.text }]}
              keyboardType="numeric"
              value={salaryMax}
              onChangeText={setSalaryMax}
              placeholder="Maximum"
              placeholderTextColor={themeColors.textSecondary}
            />
          </View>
        </View>
      </View>
      
      {/* Bottom Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.skipButton, { borderColor: themeColors.border }]}
          onPress={() => router.replace('/(tabs)/home')}
        >
          <Text style={[styles.skipButtonText, { color: themeColors.text }]}>Skip for now</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: themeColors.primary }]}
          onPress={savePreferences}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Saving...' : 'Save & Continue'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  error: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  section: {
    padding: 20,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  optionButton: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    margin: 5,
    minWidth: '30%',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  pillButton: {
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    margin: 5,
    alignItems: 'center',
  },
  pillText: {
    fontSize: 14,
    fontWeight: '500',
  },
  verticalOptions: {
    marginTop: 8,
  },
  verticalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  radioContainer: {
    marginRight: 12,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  verticalOptionText: {
    fontSize: 16,
  },
  salaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  salaryField: {
    flex: 1,
    marginHorizontal: 5,
  },
  salaryLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  salaryInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 50,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 40,
  },
  skipButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
    marginRight: 8,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flex: 2,
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
