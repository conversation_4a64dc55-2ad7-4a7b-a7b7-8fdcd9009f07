"""
Supabase-based Job Search Cache System
Replaces Redis caching with Supabase database storage for job search results
"""

import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from supabase_client import supabase

logger = logging.getLogger(__name__)

# Cache configuration
CACHE_DURATION_HOURS = 48  # Cache job search results for 48 hours
CLEANUP_BATCH_SIZE = 100   # Number of expired entries to clean up at once

class SupabaseJobCache:
    """Supabase-based job search cache"""
    
    def __init__(self):
        self.table_name = "cached_job_searches"
        
    def _generate_cache_key(self, location: str, job_types: List[str], 
                           experience: str, industries: List[str]) -> str:
        """Generate a consistent cache key from search parameters"""
        # Normalize parameters for consistent caching
        normalized_location = location.lower().strip() if location else ""
        normalized_job_types = sorted([jt.lower().strip() for jt in job_types if jt])
        normalized_experience = experience.lower().strip() if experience else ""
        normalized_industries = sorted([ind.lower().strip() for ind in industries if ind])
        
        # Create a string representation
        key_data = {
            "location": normalized_location,
            "job_types": normalized_job_types,
            "experience": normalized_experience,
            "industries": normalized_industries
        }
        
        # Generate MD5 hash for consistent key
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def get_cached_jobs(self, location: str, job_types: List[str], 
                             experience: str, industries: List[str]) -> Optional[List[Dict[str, Any]]]:
        """Get cached job search results if they exist and haven't expired"""
        try:
            cache_key = self._generate_cache_key(location, job_types, experience, industries)
            now = datetime.now()
            
            # Query for non-expired cache entries
            response = supabase.table(self.table_name).select("*").eq(
                "cache_key", cache_key
            ).gt("expires_at", now.isoformat()).execute()
            
            if response.data and len(response.data) > 0:
                cache_entry = response.data[0]
                logger.info(f"🎯 Cache HIT for key: {cache_key}")
                
                # Update access count and last accessed time
                supabase.table(self.table_name).update({
                    "access_count": cache_entry.get("access_count", 0) + 1,
                    "last_accessed_at": now.isoformat()
                }).eq("id", cache_entry["id"]).execute()
                
                return cache_entry.get("results", [])
            else:
                logger.info(f"❌ Cache MISS for key: {cache_key}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting cached jobs: {e}")
            return None
    
    async def cache_jobs(self, location: str, job_types: List[str], experience: str, 
                        industries: List[str], jobs: List[Dict[str, Any]]) -> bool:
        """Cache job search results in Supabase"""
        try:
            cache_key = self._generate_cache_key(location, job_types, experience, industries)
            now = datetime.now()
            expires_at = now + timedelta(hours=CACHE_DURATION_HOURS)
            
            # Prepare cache entry
            cache_entry = {
                "cache_key": cache_key,
                "search_params": {
                    "location": location,
                    "job_types": job_types,
                    "experience": experience,
                    "industries": industries
                },
                "results": jobs,
                "created_at": now.isoformat(),
                "expires_at": expires_at.isoformat(),
                "access_count": 0,
                "last_accessed_at": now.isoformat(),
                "job_count": len(jobs)
            }
            
            # Use upsert to handle duplicate cache keys
            response = supabase.table(self.table_name).upsert(cache_entry).execute()
            
            if response.data:
                logger.info(f"💾 Cached {len(jobs)} jobs for key: {cache_key} (expires: {expires_at})")
                return True
            else:
                logger.error(f"Failed to cache jobs for key: {cache_key}")
                return False
                
        except Exception as e:
            logger.error(f"Error caching jobs: {e}")
            return False
    
    async def cleanup_expired_cache(self) -> int:
        """Remove expired cache entries"""
        try:
            now = datetime.now()
            
            # Get expired entries
            response = supabase.table(self.table_name).select("id").lt(
                "expires_at", now.isoformat()
            ).limit(CLEANUP_BATCH_SIZE).execute()
            
            if response.data and len(response.data) > 0:
                expired_ids = [entry["id"] for entry in response.data]
                
                # Delete expired entries
                delete_response = supabase.table(self.table_name).delete().in_(
                    "id", expired_ids
                ).execute()
                
                deleted_count = len(expired_ids)
                logger.info(f"🧹 Cleaned up {deleted_count} expired cache entries")
                return deleted_count
            else:
                logger.info("🧹 No expired cache entries to clean up")
                return 0
                
        except Exception as e:
            logger.error(f"Error cleaning up expired cache: {e}")
            return 0
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            now = datetime.now()
            
            # Get total cache entries
            total_response = supabase.table(self.table_name).select(
                "id", count="exact"
            ).execute()
            total_entries = total_response.count or 0
            
            # Get active (non-expired) entries
            active_response = supabase.table(self.table_name).select(
                "id", count="exact"
            ).gt("expires_at", now.isoformat()).execute()
            active_entries = active_response.count or 0
            
            # Get expired entries
            expired_entries = total_entries - active_entries
            
            # Get most accessed cache entries
            popular_response = supabase.table(self.table_name).select(
                "cache_key, search_params, access_count, created_at"
            ).gt("expires_at", now.isoformat()).order(
                "access_count", desc=True
            ).limit(5).execute()
            
            return {
                "total_entries": total_entries,
                "active_entries": active_entries,
                "expired_entries": expired_entries,
                "cache_hit_ratio": "N/A",  # Would need separate tracking
                "popular_searches": popular_response.data or [],
                "cache_duration_hours": CACHE_DURATION_HOURS,
                "last_updated": now.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }

# Global cache instance
job_cache = SupabaseJobCache()

# Helper functions for easy use
async def get_cached_job_search(location: str, job_types: List[str], 
                               experience: str, industries: List[str]) -> Optional[List[Dict[str, Any]]]:
    """Get cached job search results"""
    return await job_cache.get_cached_jobs(location, job_types, experience, industries)

async def cache_job_search_results(location: str, job_types: List[str], experience: str, 
                                  industries: List[str], jobs: List[Dict[str, Any]]) -> bool:
    """Cache job search results"""
    return await job_cache.cache_jobs(location, job_types, experience, industries, jobs)

async def cleanup_expired_job_cache() -> int:
    """Clean up expired cache entries"""
    return await job_cache.cleanup_expired_cache()

async def get_job_cache_stats() -> Dict[str, Any]:
    """Get job cache statistics"""
    return await job_cache.get_cache_stats()
