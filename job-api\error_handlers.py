"""
Centralized error handling for the job API
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
import traceback
from typing import Dict, Any
import time

logger = logging.getLogger(__name__)


class APIError(Exception):
    """Base API error class"""
    def __init__(self, message: str, status_code: int = 500, details: Dict[str, Any] = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ExternalAPIError(APIError):
    """Error when external API calls fail"""
    def __init__(self, api_name: str, message: str, status_code: int = 503):
        super().__init__(
            message=f"External API error ({api_name}): {message}",
            status_code=status_code,
            details={"api_name": api_name}
        )


class DatabaseError(APIError):
    """Error when database operations fail"""
    def __init__(self, message: str, operation: str = None):
        super().__init__(
            message=f"Database error: {message}",
            status_code=500,
            details={"operation": operation}
        )


class ValidationError(APIError):
    """Error when input validation fails"""
    def __init__(self, message: str, field: str = None):
        super().__init__(
            message=f"Validation error: {message}",
            status_code=400,
            details={"field": field}
        )


class RateLimitError(APIError):
    """Error when rate limit is exceeded"""
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(
            message=message,
            status_code=429
        )


class AuthenticationError(APIError):
    """Error when authentication fails"""
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            status_code=401
        )


def create_error_response(
    status_code: int,
    message: str,
    details: Dict[str, Any] = None,
    request_id: str = None
) -> JSONResponse:
    """Create standardized error response"""
    
    error_response = {
        "error": {
            "message": message,
            "status_code": status_code,
            "timestamp": time.time(),
        }
    }
    
    if details:
        error_response["error"]["details"] = details
    
    if request_id:
        error_response["error"]["request_id"] = request_id
    
    return JSONResponse(
        status_code=status_code,
        content=error_response
    )


async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """Handle custom API errors"""
    request_id = getattr(request.state, 'request_id', None)
    
    logger.error(
        f"API Error: {exc.message}",
        extra={
            "status_code": exc.status_code,
            "details": exc.details,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return create_error_response(
        status_code=exc.status_code,
        message=exc.message,
        details=exc.details,
        request_id=request_id
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    request_id = getattr(request.state, 'request_id', None)
    
    logger.warning(
        f"HTTP Exception: {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return create_error_response(
        status_code=exc.status_code,
        message=str(exc.detail),
        request_id=request_id
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle request validation errors"""
    request_id = getattr(request.state, 'request_id', None)
    
    # Extract validation error details
    errors = []
    for error in exc.errors():
        errors.append({
            "field": " -> ".join(str(x) for x in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    logger.warning(
        f"Validation Error: {len(errors)} validation errors",
        extra={
            "validation_errors": errors,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return create_error_response(
        status_code=422,
        message="Request validation failed",
        details={"validation_errors": errors},
        request_id=request_id
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions"""
    request_id = getattr(request.state, 'request_id', None)
    
    logger.error(
        f"Unexpected error: {str(exc)}",
        extra={
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc(),
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        },
        exc_info=True
    )
    
    # Don't expose internal error details in production
    message = "Internal server error"
    details = None
    
    if logger.isEnabledFor(logging.DEBUG):
        message = str(exc)
        details = {"exception_type": type(exc).__name__}
    
    return create_error_response(
        status_code=500,
        message=message,
        details=details,
        request_id=request_id
    )


def setup_error_handlers(app):
    """Setup error handlers for the FastAPI app"""
    
    # Custom API errors
    app.add_exception_handler(APIError, api_error_handler)
    
    # FastAPI HTTP exceptions
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # Validation errors
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # General exceptions
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Error handlers configured")
