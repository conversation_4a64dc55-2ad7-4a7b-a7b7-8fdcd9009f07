import os
import logging
from supabase import create_client, Client
from dotenv import load_dotenv
import asyncio
from typing import Optional, Dict, Any, List
import time
from functools import wraps

load_dotenv()

logger = logging.getLogger(__name__)

SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    raise RuntimeError("Missing SUPABASE_URL or SUPABASE_KEY in .env")

# Create Supabase client with basic configuration
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)


class DatabaseManager:
    """Simple database manager"""

    def __init__(self, client: Client):
        self.client = client

    def safe_select(self, table: str, columns: str = "*", **filters) -> Dict[str, Any]:
        """Safe select operation"""
        try:
            query = self.client.table(table).select(columns)

            # Apply filters
            for key, value in filters.items():
                if value is not None:
                    query = query.eq(key, value)

            result = query.execute()
            return {"data": result.data, "error": None}
        except Exception as e:
            print(f"Select operation failed: {str(e)}")
            return {"data": [], "error": str(e)}


# Create global database manager instance
db_manager = DatabaseManager(supabase)