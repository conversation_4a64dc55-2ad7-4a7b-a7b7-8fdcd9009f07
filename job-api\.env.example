# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key

# Cache removed - using direct Supabase storage

# API Keys
JOOBLE_API_KEY=your-jooble-api-key
MUSE_API_KEY=your-muse-api-key
OPENROUTER_API_KEY=your-openrouter-api-key
RAPIDAPI_KEY=your-rapidapi-key
ASHBY_JOB_BOARD_NAME=your-ashby-board-name

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Cache removed - using direct Supabase storage

# Monitoring
PROMETHEUS_ENABLED=true
METRICS_PORT=9090

# Security
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
API_KEY_HEADER=X-API-Key
