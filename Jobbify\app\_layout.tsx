// Import polyfills first to fix WebCrypto API warning
import 'react-native-get-random-values';

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useFonts } from 'expo-font';
import { Slot, Stack, useSegments, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect, useState, useCallback } from 'react';
import { AppProvider, useAppContext } from '../context/AppContext';
import { Text, View, ActivityIndicator, TouchableOpacity } from 'react-native';
import { supabase } from '../lib/supabase';
import { Session } from '@supabase/supabase-js';
import { LightTheme, DarkTheme } from '../constants/Theme';
import React from 'react';

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    PoppinsRegular: require('../assets/fonts/Poppins-Regular.ttf'),
    PoppinsMedium: require('../assets/fonts/Poppins-Medium.ttf'),
    PoppinsBold: require('../assets/fonts/Poppins-Bold.ttf'),
    ...FontAwesome.font,
  });
  const [fontTimeout, setFontTimeout] = useState(false);

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Add a timeout for font loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!loaded) {
        setFontTimeout(true);
        console.warn('Font loading timeout!');
      }
    }, 3000);
    return () => clearTimeout(timer);
  }, [loaded]);

  if (!loaded && !fontTimeout) {
    return null;
  }

  if (fontTimeout && !loaded) {
    return (
      <Text style={{ color: 'red', marginTop: 100, fontSize: 20 }}>
        Font loading failed or is taking too long!
      </Text>
    );
  }

  return (
    <AppProvider>
      <RootLayoutNav />
    </AppProvider>
  );
}

function RootLayoutNav() {
  const { user, setUser, theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const sessionCheckedRef = React.useRef(false);

  // Memoize the user setter to prevent unnecessary re-renders
  const handleUserUpdate = useCallback(async (sessionUser: any) => {
    if (!sessionUser) {
      setUser(null);
      return;
    }

    console.log('🔄 Starting handleUserUpdate for:', sessionUser.email);

    // Add a timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('User update timeout')), 10000); // 10 second timeout
    });

    try {
      await Promise.race([
        (async () => {
          // First, create or update the user profile in Supabase
          console.log('📊 Fetching user profile...');
          const { data: existingProfile, error: fetchError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', sessionUser.id)
            .single();

          let profileData = existingProfile;

          // If profile doesn't exist, create it
          if (fetchError && fetchError.code === 'PGRST116') {
            console.log('➕ Creating new user profile for:', sessionUser.email);

            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                id: sessionUser.id,
                name: sessionUser.user_metadata?.name || sessionUser.email?.split('@')[0] || 'User',
                email: sessionUser.email || '',
                user_type: 'job_seeker' // Default to job seeker
              })
              .select('*')
              .single();

            if (createError) {
              console.error('❌ Error creating profile:', createError);
              // If it's a foreign key error, the auth session might be stale
              if (createError.code === '23503') {
                console.log('🔄 Foreign key error detected, clearing auth session...');
                await supabase.auth.signOut();
                return;
              }
            } else {
              profileData = newProfile;
              console.log('✅ Profile created successfully:', newProfile);
            }
          } else if (fetchError) {
            console.error('❌ Error fetching profile:', fetchError);
          } else {
            console.log('✅ Profile found:', existingProfile);
          }

          // Set user state with profile data
          const userData = {
            id: sessionUser.id,
            name: profileData?.name || sessionUser.user_metadata?.name || sessionUser.email?.split('@')[0] || 'User',
            email: sessionUser.email || '',
            avatar: sessionUser.user_metadata?.avatar_url || '',
            skills: [],
            experience: [],
            userType: profileData?.user_type || 'job_seeker',
            onboardingCompleted: profileData?.onboarding_completed || false,
          };

          console.log('👤 Setting user state:', userData);
          setUser(userData);
        })(),
        timeoutPromise
      ]);

    } catch (error) {
      console.error('❌ Error in handleUserUpdate:', error);

      // Fallback: set user with basic info
      const fallbackUser = {
        id: sessionUser.id,
        name: sessionUser.user_metadata?.name || sessionUser.email?.split('@')[0] || 'User',
        email: sessionUser.email || '',
        avatar: sessionUser.user_metadata?.avatar_url || '',
        skills: [],
        experience: [],
        userType: 'job_seeker' as 'job_seeker' | 'service_provider',
        onboardingCompleted: false,
      };

      console.log('🔄 Using fallback user data:', fallbackUser);
      setUser(fallbackUser);
    }
  }, [setUser]);

  // Check for an existing session when the app loads
  useEffect(() => {
    // Prevent multiple session checks
    if (sessionCheckedRef.current) return;
    sessionCheckedRef.current = true;

    console.log('🔄 Starting initial session check...');

    // Add a fallback timeout to ensure loading doesn't hang forever
    const fallbackTimeout = setTimeout(() => {
      console.log('⏰ Fallback timeout reached, setting loading to false');
      setIsLoading(false);
    }, 15000); // 15 second fallback

    supabase.auth.getSession().then(async ({ data: { session }, error }) => {
      console.log('📱 Initial session check result:', {
        hasSession: !!session,
        userEmail: session?.user?.email,
        error: error?.message
      });

      setSession(session);

      try {
        if (session?.user) {
          console.log('👤 User found, updating user state...');
          await handleUserUpdate(session.user);
        } else {
          console.log('❌ No user session found');
        }
      } catch (updateError) {
        console.error('❌ Error updating user:', updateError);
      } finally {
        console.log('✅ Setting loading to false');
        clearTimeout(fallbackTimeout);
        setIsLoading(false);
      }
    }).catch((sessionError) => {
      console.error('❌ Error getting session:', sessionError);
      clearTimeout(fallbackTimeout);
      setIsLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state change:', event, session?.user?.email);
      setSession(session);

      try {
        if (session?.user) {
          console.log('👤 User signed in, updating user state...');
          await handleUserUpdate(session.user);
        } else {
          console.log('👤 User signed out, clearing user state...');
          setUser(null);
        }
      } catch (updateError) {
        console.error('❌ Error in auth state change:', updateError);
      } finally {
        console.log('✅ Auth state change complete, setting loading to false');
        setIsLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, [handleUserUpdate]);

  // Route protection logic moved into its own effect to avoid calling hook inside hook
  const segments = useSegments();
  const router = useRouter();
  const processingRef = React.useRef(false);
  const prevSegmentsRef = React.useRef(segments);
  const prevUserRef = React.useRef(user);

  useEffect(() => {
    // Don't run navigation logic while still loading
    if (isLoading) return;
    
    // Skip if we're already processing to prevent multiple redirects
    if (processingRef.current) {
      return;
    }
    
    // Skip if nothing has changed since last check
    if (
      segments === prevSegmentsRef.current &&
      user === prevUserRef.current
    ) {
      return;
    }
    
    // Update refs
    prevSegmentsRef.current = segments;
    prevUserRef.current = user;
    
    // Check if we're in the auth group
    const inAuthGroup = segments[0] === '(auth)';
    
    // Set processing flag to prevent multiple redirects
    processingRef.current = true;
    
    // IMPORTANT: Add a delay to ensure the root layout is fully mounted
    // This helps prevent the "Attempted to navigate before mounting the Root Layout" error
    const timer = setTimeout(() => {
      try {
        console.log('Attempting navigation based on auth state:', {
          user: !!user,
          inAuthGroup,
          currentSegment: segments[0]
        });
        
        // If user is not signed in and the initial segment is not in the auth group, redirect to login
        if (!user && !inAuthGroup && segments[0] !== 'splash') {
          router.replace('/splash');
        // If user is signed in and the initial segment is in the auth group, redirect to home
        } else if (user && inAuthGroup && segments[0] !== '(tabs)') {
          router.replace('/');
        }
      } catch (navError) {
        console.error('Navigation error in root layout:', navError);
        // If navigation fails, we'll just stay on the current screen
      } finally {
        // Clear the processing flag
        processingRef.current = false;
      }
    }, 300); // Use a longer delay to ensure everything is ready
    
    // Clean up the timer if the component unmounts
    return () => clearTimeout(timer);
  }, [segments, user, router, isLoading]);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: themeColors.background }}>
        <ActivityIndicator size="large" color={themeColors.tint} />
        <Text style={{ marginTop: 20, fontSize: 16, color: themeColors.text }}>Loading...</Text>
        <Text style={{ marginTop: 10, fontSize: 14, color: themeColors.textSecondary, textAlign: 'center', paddingHorizontal: 40 }}>
          Checking authentication status...
        </Text>
        <TouchableOpacity
          style={{
            marginTop: 30,
            padding: 12,
            backgroundColor: themeColors.primary,
            borderRadius: 8,
          }}
          onPress={() => {
            console.log('🔄 Force stopping loading state');
            setIsLoading(false);
          }}
        >
          <Text style={{ color: 'white', fontSize: 16 }}>Skip Loading</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            marginTop: 10,
            padding: 12,
            backgroundColor: '#666',
            borderRadius: 8,
          }}
          onPress={() => {
            console.log('🔧 Opening debug screen');
            router.push('/debug');
          }}
        >
          <Text style={{ color: 'white', fontSize: 16 }}>Debug Info</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Return a Slot instead of conditional Stack to fix the warning
  return <Slot />;
}
