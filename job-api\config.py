"""
Configuration management for the job API
"""

import os
from typing import List, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database Configuration
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_key: str = Field(..., env="SUPABASE_KEY")
    db_pool_size: int = Field(default=10, env="DB_POOL_SIZE")
    db_retry_attempts: int = Field(default=3, env="DB_RETRY_ATTEMPTS")
    db_retry_delay: float = Field(default=1.0, env="DB_RETRY_DELAY")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    redis_max_connections: int = Field(default=20, env="REDIS_MAX_CONNECTIONS")
    redis_retry_attempts: int = Field(default=3, env="REDIS_RETRY_ATTEMPTS")
    redis_retry_delay: float = Field(default=0.1, env="REDIS_RETRY_DELAY")
    
    # API Keys
    jooble_api_key: str = Field(default="", env="JOOBLE_API_KEY")
    muse_api_key: str = Field(default="", env="MUSE_API_KEY")
    openrouter_api_key: str = Field(default="", env="OPENROUTER_API_KEY")
    rapidapi_key: str = Field(default="", env="RAPIDAPI_KEY")
    ashby_job_board_name: str = Field(default="ashby", env="ASHBY_JOB_BOARD_NAME")
    adzuna_app_id: str = Field(default="", env="ADZUNA_APP_ID")
    adzuna_app_key: str = Field(default="", env="ADZUNA_APP_KEY")
    
    # Rate Limiting
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    rate_limit_per_hour: int = Field(default=1000, env="RATE_LIMIT_PER_HOUR")
    
    # Cache Configuration
    cache_ttl_hours: int = Field(default=24, env="CACHE_TTL_HOURS")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # Security
    cors_origins: str = Field(default="http://localhost:3000", env="CORS_ORIGINS")
    trusted_hosts: str = Field(default="localhost,127.0.0.1", env="TRUSTED_HOSTS")
    api_key_header: str = Field(default="X-API-Key", env="API_KEY_HEADER")
    
    # Monitoring
    prometheus_enabled: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    @property
    def cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list"""
        return [origin.strip() for origin in self.cors_origins.split(",")]
    
    @property
    def trusted_hosts_list(self) -> List[str]:
        """Get trusted hosts as a list"""
        return [host.strip() for host in self.trusted_hosts.split(",")]
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            "url": self.supabase_url,
            "key": self.supabase_key,
            "pool_size": self.db_pool_size,
            "retry_attempts": self.db_retry_attempts,
            "retry_delay": self.db_retry_delay
        }
    
    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration"""
        return {
            "url": self.redis_url,
            "max_connections": self.redis_max_connections,
            "retry_attempts": self.redis_retry_attempts,
            "retry_delay": self.redis_retry_delay
        }
    
    def get_api_keys(self) -> Dict[str, str]:
        """Get all API keys"""
        return {
            "jooble": self.jooble_api_key,
            "muse": self.muse_api_key,
            "openrouter": self.openrouter_api_key,
            "rapidapi": self.rapidapi_key,
            "ashby_board_name": self.ashby_job_board_name,
            "adzuna_app_id": self.adzuna_app_id,
            "adzuna_app_key": self.adzuna_app_key
        }


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings
