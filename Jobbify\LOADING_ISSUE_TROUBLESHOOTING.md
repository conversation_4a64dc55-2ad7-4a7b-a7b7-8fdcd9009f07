# Loading Issue Troubleshooting Guide

## 🔍 Problem: App Stuck on Loading Screen

The app is showing a loading screen but never progresses to the login screen. This guide will help you debug and fix the issue.

## 🛠️ Immediate Solutions

### 1. **Use the Skip Loading Button**
- When the loading screen appears, you'll now see a "Skip Loading" button
- Tap it to force the app to continue
- This will help you get past the loading state

### 2. **Use the Debug Screen**
- Tap the "Debug Info" button on the loading screen
- This will show you detailed information about:
  - App context state
  - Supabase authentication status
  - Database connection
  - User data

### 3. **Manual Navigation**
- If you can access the debug screen, use the "Go to Login" button
- This will manually navigate you to the login screen

## 🔧 Debugging Steps

### Step 1: Check Console Logs
Look for these log messages in your development console:

```
🔄 Starting initial session check...
📱 Initial session check result: { hasSession: false, userEmail: undefined }
❌ No user session found
✅ Setting loading to false
```

### Step 2: Test Supabase Connection
Run the connection test script:

```bash
cd Jobbify
node scripts/test-supabase-connection.js
```

This will verify:
- ✅ Supabase URL and key are correct
- ✅ Database tables are accessible
- ✅ Authentication system is working
- ✅ RLS policies are active

### Step 3: Check Network Connection
- Ensure your device/emulator has internet access
- Try accessing https://ubueawlkwlvgzxcslats.supabase.co in a browser
- Check if your firewall is blocking the connection

## 🐛 Common Causes & Solutions

### 1. **Supabase Connection Issues**
**Symptoms**: App stuck loading, no console logs about session check

**Solutions**:
- Check internet connection
- Verify Supabase project is active
- Confirm API keys are correct in `lib/supabase.ts`

### 2. **Authentication State Loop**
**Symptoms**: Loading screen appears briefly then reappears

**Solutions**:
- Clear app data/cache
- Restart the development server
- Check for infinite loops in auth state changes

### 3. **Database Query Hanging**
**Symptoms**: Session check starts but never completes

**Solutions**:
- Check database connection
- Verify RLS policies aren't blocking queries
- Look for slow database queries

### 4. **React Native Metro Issues**
**Symptoms**: App doesn't update with new code

**Solutions**:
```bash
# Clear Metro cache
npx react-native start --reset-cache

# Or for Expo
npx expo start -c
```

### 5. **Profile Creation Timeout**
**Symptoms**: User found but profile creation hangs

**Solutions**:
- Check database permissions
- Verify profiles table structure
- Look for foreign key constraint issues

## 🔍 Debug Information Available

### App Context State
- `isLoading`: Whether app is in loading state
- `user`: Current user object (null if not logged in)
- `session`: Supabase session object

### Supabase Auth State
- `hasSession`: Whether there's an active session
- `user`: Auth user object
- `error`: Any authentication errors

### Database Connection
- Connection status to Supabase
- Table accessibility
- RLS policy status

## 🚀 Quick Fixes

### Fix 1: Force Skip Loading
```typescript
// In the loading screen, tap "Skip Loading" button
// This sets isLoading to false manually
```

### Fix 2: Clear Auth Data
```typescript
// In debug screen, tap "Clear Auth Data"
// This signs out any existing sessions
```

### Fix 3: Manual Navigation
```typescript
// In debug screen, tap "Go to Login"
// This manually navigates to login screen
```

## 🔄 Recovery Steps

### If App is Completely Stuck:

1. **Force close the app** and restart
2. **Clear app data** (Android) or delete and reinstall (iOS)
3. **Restart development server** with cache clear
4. **Check Supabase dashboard** for any service issues

### If Loading Persists:

1. **Use Skip Loading button** to bypass
2. **Check debug information** for specific errors
3. **Run connection test script** to verify backend
4. **Check console logs** for detailed error messages

## 📱 Platform-Specific Issues

### iOS Simulator
- Network requests might be slower
- Clear simulator data: Device → Erase All Content and Settings

### Android Emulator
- Check if emulator has internet access
- Try cold boot: AVD Manager → Cold Boot Now

### Physical Devices
- Ensure device and development machine are on same network
- Check if corporate firewall is blocking Supabase

## 🎯 Expected Behavior

### Normal Flow:
1. **App starts** → Shows loading screen
2. **Session check** → Logs appear in console
3. **No user found** → Navigates to login screen
4. **User found** → Checks onboarding status
5. **Onboarding complete** → Navigates to home
6. **Onboarding incomplete** → Navigates to preferences

### With Debug Features:
1. **Loading screen** → Shows skip and debug buttons
2. **Skip button** → Forces navigation to continue
3. **Debug button** → Shows detailed state information
4. **Manual navigation** → Allows direct screen access

## 📞 Getting Help

If the issue persists:

1. **Check console logs** for specific error messages
2. **Run the connection test** and share results
3. **Use debug screen** to gather state information
4. **Try the quick fixes** listed above
5. **Clear all data** and start fresh if needed

The enhanced loading screen now provides multiple ways to debug and bypass loading issues, making it much easier to identify and resolve the problem!
