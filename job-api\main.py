# main.py
from fastapi import FastAP<PERSON>, Depends, HTTPException, status, Response, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn
import os
import logging
from jobs import router as jobs_router        # ← imports jobs router
from schema import router as schema_router    # ← imports schema router
# Personalized search functionality moved to job_utils.py
from pydantic import BaseModel, validator, Field
from supabase_client import supabase
from typing import Optional, List, Dict, Any
import traceback
import asyncio
import httpx
from datetime import datetime, timedelta
from job_apis import jooble, map_jooble_job, muse, map_muse_job, ashby, map_ashby_job
from job_service import adzuna, arbeitnow, map_adzuna_job, map_arbeitnow_job
import time
from collections import defaultdict
import uuid

# Import error handling
from error_handlers import (
    setup_error_handlers, APIError, ExternalAPIError, DatabaseError,
    ValidationError, RateLimitError, AuthenticationError
)

# Setup logging
def setup_logging():
    """Configure logging for the application"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

def get_logger(name: str):
    """Get a logger instance"""
    return logging.getLogger(name)

setup_logging()
logger = get_logger(__name__)

# Configuration
RATE_LIMIT_PER_MINUTE = int(os.getenv("RATE_LIMIT_PER_MINUTE", "60"))
RATE_LIMIT_PER_HOUR = int(os.getenv("RATE_LIMIT_PER_HOUR", "1000"))
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000").split(",")

# Rate limiting storage
rate_limit_storage = defaultdict(lambda: {"minute": [], "hour": []})

app = FastAPI(
    title="Jobbify API",
    description="""
    # Jobbify Job Search API

    A comprehensive job search and management API that aggregates job listings from multiple sources,
    provides intelligent job matching, and offers advanced caching and analytics capabilities.

    ## Features

    - **Multi-source job aggregation**: Integrates with Jooble, The Muse, Ashby, Adzuna, and Arbeitnow APIs
    - **Direct database access**: All data stored and retrieved from Supabase backend
    - **Real-time search**: Dynamic job search with location and preference filtering
    - **Job evaluation**: AI-powered job quality scoring and analysis
    - **User management**: Bookmark, application, and preference tracking
    - **Rate limiting**: Built-in rate limiting for API protection
    - **Comprehensive monitoring**: Prometheus metrics and health checks

    ## Authentication

    Most endpoints require a valid `profile_id` parameter for user identification.

    ## Rate Limits

    - 60 requests per minute per IP
    - 1000 requests per hour per IP

    ## Error Handling

    All endpoints return standardized error responses with appropriate HTTP status codes.
    """,
    version="1.0.0",
    contact={
        "name": "Jobbify Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
    },
    docs_url="/docs" if os.getenv("DEBUG", "false").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "false").lower() == "true" else None
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "**********", "********", "*.yourdomain.com"]
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# ─── Request ID Middleware ─────────────────────────────────────────────

async def request_id_middleware(request: Request, call_next):
    """Add unique request ID to each request"""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id

    start_time = time.time()
    response = await call_next(request)
    duration = (time.time() - start_time) * 1000  # Convert to milliseconds

    # Add request ID to response headers
    response.headers["X-Request-ID"] = request_id

    # Log request completion
    logger.info(
        f"{request.method} {request.url.path} completed",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "duration": duration,
            "client_ip": request.client.host
        }
    )

    return response

# ─── Rate Limiting Middleware ──────────────────────────────────────────

async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware"""
    client_ip = request.client.host
    current_time = time.time()

    # Clean old entries
    rate_limit_storage[client_ip]["minute"] = [
        t for t in rate_limit_storage[client_ip]["minute"]
        if current_time - t < 60
    ]
    rate_limit_storage[client_ip]["hour"] = [
        t for t in rate_limit_storage[client_ip]["hour"]
        if current_time - t < 3600
    ]

    # Check rate limits
    if len(rate_limit_storage[client_ip]["minute"]) >= RATE_LIMIT_PER_MINUTE:
        raise RateLimitError("Rate limit exceeded: too many requests per minute")

    if len(rate_limit_storage[client_ip]["hour"]) >= RATE_LIMIT_PER_HOUR:
        raise RateLimitError("Rate limit exceeded: too many requests per hour")

    # Record this request
    rate_limit_storage[client_ip]["minute"].append(current_time)
    rate_limit_storage[client_ip]["hour"].append(current_time)

    response = await call_next(request)
    return response

app.middleware("http")(request_id_middleware)
app.middleware("http")(rate_limit_middleware)

# Setup error handlers
setup_error_handlers(app)

# ─── Models ────────────────────────────────────────────────────────────

class SwipeIn(BaseModel):
    job_id: str
    direction: str  # 'left' or 'right'
    profile_id: str  # Adding profile_id to support current app structure
    
    # Validate that direction is either 'left' or 'right'
    @validator('direction')
    def validate_direction(cls, v):
        if v not in ['left', 'right']:
            raise ValueError('direction must be either "left" or "right"')
        return v

class BookmarkIn(BaseModel):
    job_id: str
    profile_id: str  # Adding profile_id to support current app structure

class ApplicationIn(BaseModel):
    job_id: str
    profile_id: str  # Adding profile_id to support current app structure
    cover_letter: Optional[str] = None

class ApplicationStatusUpdate(BaseModel):
    status: str = Field(..., description="New application status")
    notes: Optional[str] = Field(None, description="Optional notes for status change")
    admin_user_id: str = Field(..., description="ID of admin user making the change")

class BulkStatusUpdate(BaseModel):
    application_ids: List[str] = Field(..., description="List of application IDs to update")
    status: str = Field(..., description="New status for all applications")
    notes: Optional[str] = Field(None, description="Optional notes for status change")
    admin_user_id: str = Field(..., description="ID of admin user making the change")

class JobSearchRequest(BaseModel):
    keywords: str = Field(..., min_length=1, max_length=200, description="Job search keywords")
    location: str = Field(..., min_length=1, max_length=100, description="Job location")
    job_type: Optional[str] = Field(None, max_length=50, description="Job type filter")
    experience_level: Optional[str] = Field(None, max_length=50, description="Experience level filter")
    min_salary: Optional[int] = Field(None, ge=0, le=1000000, description="Minimum salary")
    max_salary: Optional[int] = Field(None, ge=0, le=1000000, description="Maximum salary")

    @validator('max_salary')
    def validate_salary_range(cls, v, values):
        if v is not None and 'min_salary' in values and values['min_salary'] is not None:
            if v < values['min_salary']:
                raise ValueError('max_salary must be greater than or equal to min_salary')
        return v

# ─── Dependency to get current user ID ───────────────────────────────────
# For now, we'll use a simpler approach without auth verification
def get_user_id(profile_id: str = None) -> str:
    if not profile_id:
        raise HTTPException(status_code=401, detail="Missing profile_id")
    return profile_id

# ─── Endpoints ────────────────────────────────────────────────────────

@app.get("/jobs/")
def fetch_jobs(limit: int = 50) -> List[Dict[str, Any]]:
    """Fetch available jobs."""
    try:
        resp = supabase.table("jobs").select("*").limit(limit).execute()
        return resp.data
    except Exception as e:
        # Return mock data if Supabase fails
        print(f"Supabase error: {str(e)}")
        return [
            {
                "id": "test-job-1",
                "title": "Senior Software Engineer",
                "company": "Tech Corp",
                "location": "Remote",
                "description": "We are looking for a senior software engineer to join our team.",
                "salary": "$120,000 - $150,000",
                "url": "https://example.com/job1",
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "test-job-2",
                "title": "Frontend Developer",
                "company": "Design Studio",
                "location": "New York, NY",
                "description": "Join our creative team as a frontend developer.",
                "salary": "$90,000 - $110,000",
                "url": "https://example.com/job2",
                "created_at": "2024-01-02T00:00:00Z"
            }
        ][:limit]

@app.get("/jobs/unseen")
def fetch_unseen_jobs(limit: int = 20, profile_id: str = None) -> List[Dict[str, Any]]:
    """Call the RPC unseen_jobs to return jobs obeying your rules."""
    user_id = get_user_id(profile_id)
    try:
        resp = supabase.rpc("unseen_jobs", {"_limit": limit, "user_id": user_id}).execute()
        return resp.data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/jobs/search")
async def search_jobs_realtime(request: JobSearchRequest) -> Dict[str, Any]:
    """Search for jobs in real-time based on user preferences"""
    try:
        print(f"🔍 Real-time job search: {request.keywords} in {request.location}")

        # Fetch jobs from multiple APIs concurrently
        async with httpx.AsyncClient() as client:
            tasks = []

            # Jooble API
            tasks.append(jooble(client, request.keywords, request.location))

            # The Muse API (if relevant category)
            if any(keyword in request.keywords.lower() for keyword in ['technology', 'computer', 'software', 'developer', 'engineer']):
                tasks.append(muse(client, "Computer and IT", request.location))

            # Adzuna API
            tasks.append(adzuna(client))

            # Arbeitnow API
            tasks.append(arbeitnow(client))

            # Execute all API calls concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            all_jobs = []

            # Process Jooble results
            if len(results) > 0 and not isinstance(results[0], Exception):
                jooble_jobs = results[0]
                for job in jooble_jobs:
                    mapped_job = map_jooble_job(job)
                    all_jobs.append(mapped_job)

            # Process Muse results
            if len(results) > 1 and not isinstance(results[1], Exception):
                muse_jobs = results[1]
                for job in muse_jobs:
                    mapped_job = map_muse_job(job)
                    all_jobs.append(mapped_job)

            # Process Adzuna results
            if len(results) > 2 and not isinstance(results[2], Exception):
                adzuna_jobs = results[2]
                for job in adzuna_jobs:
                    mapped_job = map_adzuna_job(job)
                    all_jobs.append(mapped_job)

            # Process Arbeitnow results
            if len(results) > 3 and not isinstance(results[3], Exception):
                arbeitnow_jobs = results[3]
                for job in arbeitnow_jobs:
                    mapped_job = map_arbeitnow_job(job)
                    all_jobs.append(mapped_job)

        # Filter jobs based on search criteria
        filtered_jobs = filter_jobs_by_criteria(all_jobs, request)

        # Remove duplicates
        unique_jobs = remove_duplicate_jobs(filtered_jobs)

        # Sort by relevance
        sorted_jobs = sort_jobs_by_relevance(unique_jobs, request)

        print(f"✅ Found {len(sorted_jobs)} relevant jobs")

        return {
            "jobs": sorted_jobs[:50],  # Limit to 50 jobs
            "total_found": len(sorted_jobs),
            "search_params": request.dict()
        }

    except Exception as e:
        print(f"❌ Error in real-time job search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Job search failed: {str(e)}")

def filter_jobs_by_criteria(jobs: List[Dict], request: JobSearchRequest) -> List[Dict]:
    """Filter jobs based on search criteria"""
    filtered = []

    for job in jobs:
        try:
            # Location filter
            if request.location.lower() != 'remote':
                job_location = job.get('location', '')
                # Safely convert to string and lowercase
                if isinstance(job_location, dict):
                    job_location = str(job_location.get('display_name', ''))
                job_location = str(job_location).lower()

                if request.location.lower() not in job_location and 'remote' not in job_location:
                    continue

            # Salary filter
            if request.min_salary or request.max_salary:
                job_salary = job.get('salary', '')
                if job_salary:
                    # Extract salary numbers (basic implementation)
                    import re
                    salary_numbers = re.findall(r'\d+', str(job_salary))
                    if salary_numbers:
                        job_salary_num = int(salary_numbers[0])
                        if request.min_salary and job_salary_num < request.min_salary:
                            continue
                        if request.max_salary and job_salary_num > request.max_salary:
                            continue

            # Job type filter
            if request.job_type:
                job_title = job.get('title', '')
                job_description = job.get('description', '')

                # Safely convert to string and lowercase
                job_title = str(job_title).lower()
                job_description = str(job_description).lower()

                if request.job_type.lower() not in job_title and request.job_type.lower() not in job_description:
                    continue

            filtered.append(job)
        except Exception as e:
            print(f"⚠️ Error filtering job {job.get('title', 'Unknown')}: {e}")
            # Skip this job if there's an error processing it
            continue

    return filtered

def remove_duplicate_jobs(jobs: List[Dict]) -> List[Dict]:
    """Remove duplicate jobs based on title and company"""
    seen = set()
    unique_jobs = []

    for job in jobs:
        try:
            title = str(job.get('title', '')).lower().strip()
            company = str(job.get('company', '')).lower().strip()
            key = f"{title}_{company}"

            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)
        except Exception as e:
            print(f"⚠️ Error processing job for deduplication: {e}")
            # Add the job anyway to avoid losing data
            unique_jobs.append(job)

    return unique_jobs

def sort_jobs_by_relevance(jobs: List[Dict], request: JobSearchRequest) -> List[Dict]:
    """Sort jobs by relevance to search criteria"""
    def calculate_relevance_score(job):
        try:
            score = 0
            title = str(job.get('title', '')).lower()
            description = str(job.get('description', '')).lower()
            location = str(job.get('location', '')).lower()

            # Keyword relevance
            keywords = request.keywords.lower().split()
            for keyword in keywords:
                if keyword in title:
                    score += 10
                if keyword in description:
                    score += 5

            # Location relevance
            if request.location.lower() in location:
                score += 8
            elif 'remote' in location and request.location.lower() == 'remote':
                score += 10

            # Recent posting bonus (if available)
            if 'posted_date' in job:
                # Add bonus for recent postings
                score += 2

            return score
        except Exception as e:
            print(f"⚠️ Error calculating relevance for job {job.get('title', 'Unknown')}: {e}")
            return 0

    # Sort by relevance score (descending)
    return sorted(jobs, key=calculate_relevance_score, reverse=True)



@app.post("/swipe", status_code=status.HTTP_201_CREATED)
def swipe(sw: SwipeIn) -> Dict[str, Any]:
    """Insert a swipe record."""
    try:
        user_id = get_user_id(sw.profile_id)
        payload = {
            "user_id": user_id,
            "job_id": sw.job_id,
            "direction": sw.direction,
        }
        
        # Check if there's an existing swipe first
        print(f"Checking if swipe already exists: user_id={user_id}, job_id={sw.job_id}")
        try:
            existing = supabase.table("swipes").select("id").eq("user_id", user_id).eq("job_id", sw.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Swipe already exists, update it
                swipe_id = existing.data[0]["id"]
                print(f"Swipe exists with ID {swipe_id}, updating direction to {sw.direction}")
                
                try:
                    resp = supabase.table("swipes").update({"direction": sw.direction}).eq("id", swipe_id).execute()
                    print(f"Update response: {resp.data}")
                    return resp.data[0] if resp.data and len(resp.data) > 0 else {"id": swipe_id, "direction": sw.direction}
                except Exception as update_error:
                    print(f"Error updating swipe: {str(update_error)}")
                    print(traceback.format_exc())
                    # Return existing data as success instead of failing
                    return {"id": swipe_id, "message": "Record exists but could not be updated"}
            else:
                # No existing swipe, insert new one
                print(f"No existing swipe found, inserting new record with payload: {payload}")
                try:
                    resp = supabase.table("swipes").insert(payload).execute()
                    print(f"Insert response: {resp.data}")
                    return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
                except Exception as insert_error:
                    print(f"Error inserting swipe: {str(insert_error)}")
                    print(traceback.format_exc())
                    raise HTTPException(status_code=400, detail=f"Failed to insert swipe: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing swipes: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("swipes").insert(payload).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert swipe: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in swipe endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/bookmarks", status_code=status.HTTP_201_CREATED)
def bookmark(bm: BookmarkIn) -> Dict[str, Any]:
    """Insert a bookmark."""
    try:
        user_id = get_user_id(bm.profile_id)
        
        # Check if bookmark already exists
        print(f"Checking if bookmark already exists: profile_id={user_id}, job_id={bm.job_id}")
        try:
            existing = supabase.table("bookmarks").select("id").eq("profile_id", user_id).eq("job_id", bm.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Bookmark already exists, return success
                bookmark_id = existing.data[0]["id"]
                print(f"Bookmark already exists with ID {bookmark_id}")
                return existing.data[0]
            
            # No existing bookmark, insert new one
            print(f"No existing bookmark found, inserting new record")
            try:
                resp = supabase.table("bookmarks").insert({
                    "profile_id": user_id,
                    "job_id": bm.job_id
                }).execute()
                
                print(f"Insert response: {resp.data}")
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as insert_error:
                print(f"Error inserting bookmark: {str(insert_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert bookmark: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing bookmarks: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("bookmarks").insert({
                    "profile_id": user_id,
                    "job_id": bm.job_id
                }).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert bookmark: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in bookmark endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/applications", status_code=status.HTTP_201_CREATED)
def apply(apply_in: ApplicationIn) -> Dict[str, Any]:
    """Insert an application record."""
    try:
        user_id = get_user_id(apply_in.profile_id)
        payload = {
            "profile_id": user_id,
            "job_id": apply_in.job_id,
            "cover_letter": apply_in.cover_letter,
            "status": "applying"
        }
        
        # Check if application already exists
        print(f"Checking if application already exists: profile_id={user_id}, job_id={apply_in.job_id}")
        try:
            existing = supabase.table("applications").select("id").eq("profile_id", user_id).eq("job_id", apply_in.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Application already exists, return success
                app_id = existing.data[0]["id"]
                print(f"Application already exists with ID {app_id}")
                return existing.data[0]
            
            # No existing application, insert new one
            print(f"No existing application found, inserting new record")
            try:
                resp = supabase.table("applications").insert(payload).execute()
                
                print(f"Insert response: {resp.data}")
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as insert_error:
                print(f"Error inserting application: {str(insert_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert application: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing applications: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("applications").insert(payload).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert application: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in apply endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/matches", status_code=status.HTTP_201_CREATED)
def save_match(apply_in: ApplicationIn) -> Dict[str, Any]:
    """Insert a match record for backwards compatibility."""
    try:
        user_id = get_user_id(apply_in.profile_id)
        payload = {
            "profile_id": user_id,
            "job_id": apply_in.job_id,
            "status": "applying",
            "created_at": "now()"
        }
        
        # Check if a match already exists
        print(f"Checking if match already exists: profile_id={user_id}, job_id={apply_in.job_id}")
        try:
            existing = supabase.table("matches").select("id").eq("profile_id", user_id).eq("job_id", apply_in.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Match already exists, return success
                match_id = existing.data[0]["id"]
                print(f"Match already exists with ID {match_id}")
                return existing.data[0]
            
            # No existing match, insert new one
            print(f"No existing match found, inserting new record")
            try:
                resp = supabase.table("matches").insert(payload).execute()
                
                print(f"Insert response: {resp.data}")
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as insert_error:
                print(f"Error inserting match: {str(insert_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert match: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing matches: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("matches").insert(payload).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert match: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in save_match endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

# Add a health check endpoint
@app.get("/health")
def health_check():
    return {"status": "ok"}

@app.get("/test-connection")
def test_connection():
    """Test endpoint to verify React Native app can connect"""
    return {
        "status": "connected",
        "message": "React Native app successfully connected to job API",
        "timestamp": datetime.now().isoformat()
    }

app.include_router(jobs_router)
app.include_router(schema_router)

# Add health check endpoints
from health_checks import router as health_router
app.include_router(health_router)

# Personalized search endpoints moved to jobs router

# Add monitoring endpoints

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    from api_usage import get_api_health_status
    from health_checks import check_cache_health

    api_health = await get_api_health_status()
    cache_health = await check_cache_health()

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "cache": cache_health,
        "apis": api_health
    }

@app.get("/metrics")
async def get_metrics():
    """Prometheus-style metrics endpoint"""
    from metrics import exposition
    return Response(content=exposition(), media_type="text/plain")

@app.get("/cache/stats")
async def get_cache_stats():
    """Get Supabase cache statistics"""
    from supabase_cache import get_job_cache_stats
    return await get_job_cache_stats()

@app.post("/cache/cleanup")
async def cleanup_cache():
    """Clean up expired cache entries"""
    from supabase_cache import cleanup_expired_job_cache
    deleted_count = await cleanup_expired_job_cache()
    return {
        "message": f"Cleaned up {deleted_count} expired cache entries",
        "deleted_count": deleted_count,
        "timestamp": datetime.now().isoformat()
    }

# Application Status Management Endpoints

@app.put("/applications/{application_id}/status", status_code=status.HTTP_200_OK)
def update_application_status(
    application_id: str,
    status_update: ApplicationStatusUpdate
) -> Dict[str, Any]:
    """Update application status with admin verification and audit logging"""
    try:
        # Verify admin access
        admin_user_id = get_user_id(status_update.admin_user_id)

        # Check if user is admin
        admin_check = supabase.table("profiles").select("user_type").eq("id", admin_user_id).execute()
        if not admin_check.data or admin_check.data[0].get("user_type") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")

        # Validate status
        valid_statuses = ['applying', 'applied', 'interviewing', 'offered', 'rejected', 'accepted', 'withdrawn']
        if status_update.status not in valid_statuses:
            raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {valid_statuses}")

        # Get current application data
        current_app = supabase.table("matches").select("*").eq("id", application_id).execute()
        if not current_app.data:
            raise HTTPException(status_code=404, detail="Application not found")

        current_data = current_app.data[0]
        old_status = current_data.get("status")

        # Update application status
        update_payload = {
            "status": status_update.status,
            "updated_at": datetime.now().isoformat()
        }

        if status_update.notes:
            update_payload["admin_notes"] = status_update.notes

        result = supabase.table("matches").update(update_payload).eq("id", application_id).execute()

        if result.data:
            # Log the status change for audit trail
            audit_log = {
                "application_id": application_id,
                "user_id": current_data.get("profile_id"),
                "job_id": current_data.get("job_id"),
                "old_status": old_status,
                "new_status": status_update.status,
                "changed_by": admin_user_id,
                "change_reason": status_update.notes,
                "changed_at": datetime.now().isoformat()
            }

            # Insert audit log (create table if needed)
            try:
                supabase.table("application_status_changes").insert(audit_log).execute()
            except Exception as log_error:
                print(f"Warning: Could not log status change: {log_error}")

            return {
                "success": True,
                "message": f"Application status updated from '{old_status}' to '{status_update.status}'",
                "application_id": application_id,
                "old_status": old_status,
                "new_status": status_update.status,
                "updated_at": update_payload["updated_at"]
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update application status")

    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error updating application status: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/applications/bulk-status", status_code=status.HTTP_200_OK)
def bulk_update_application_status(bulk_update: BulkStatusUpdate) -> Dict[str, Any]:
    """Update multiple application statuses at once"""
    try:
        # Verify admin access
        admin_user_id = get_user_id(bulk_update.admin_user_id)

        # Check if user is admin
        admin_check = supabase.table("profiles").select("user_type").eq("id", admin_user_id).execute()
        if not admin_check.data or admin_check.data[0].get("user_type") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")

        # Validate status
        valid_statuses = ['applying', 'applied', 'interviewing', 'offered', 'rejected', 'accepted', 'withdrawn']
        if bulk_update.status not in valid_statuses:
            raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {valid_statuses}")

        updated_applications = []
        failed_updates = []

        for app_id in bulk_update.application_ids:
            try:
                # Get current application data
                current_app = supabase.table("matches").select("*").eq("id", app_id).execute()
                if not current_app.data:
                    failed_updates.append({"id": app_id, "error": "Application not found"})
                    continue

                current_data = current_app.data[0]
                old_status = current_data.get("status")

                # Update application status
                update_payload = {
                    "status": bulk_update.status,
                    "updated_at": datetime.now().isoformat()
                }

                if bulk_update.notes:
                    update_payload["admin_notes"] = bulk_update.notes

                result = supabase.table("matches").update(update_payload).eq("id", app_id).execute()

                if result.data:
                    updated_applications.append({
                        "id": app_id,
                        "old_status": old_status,
                        "new_status": bulk_update.status
                    })

                    # Log the status change for audit trail
                    audit_log = {
                        "application_id": app_id,
                        "user_id": current_data.get("profile_id"),
                        "job_id": current_data.get("job_id"),
                        "old_status": old_status,
                        "new_status": bulk_update.status,
                        "changed_by": admin_user_id,
                        "change_reason": f"Bulk update: {bulk_update.notes}" if bulk_update.notes else "Bulk update",
                        "changed_at": datetime.now().isoformat()
                    }

                    try:
                        supabase.table("application_status_changes").insert(audit_log).execute()
                    except Exception as log_error:
                        print(f"Warning: Could not log status change for {app_id}: {log_error}")
                else:
                    failed_updates.append({"id": app_id, "error": "Failed to update"})

            except Exception as e:
                failed_updates.append({"id": app_id, "error": str(e)})

        return {
            "success": True,
            "message": f"Bulk update completed. {len(updated_applications)} updated, {len(failed_updates)} failed",
            "updated_applications": updated_applications,
            "failed_updates": failed_updates,
            "total_requested": len(bulk_update.application_ids),
            "updated_count": len(updated_applications),
            "failed_count": len(failed_updates)
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Error in bulk status update: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))



@app.get("/api-usage/stats")
async def get_api_usage_stats():
    """Get API usage statistics"""
    from api_usage import usage_tracker
    return await usage_tracker.get_all_usage_stats()

# Add this at the end of file to bind to all interfaces when run directly
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
