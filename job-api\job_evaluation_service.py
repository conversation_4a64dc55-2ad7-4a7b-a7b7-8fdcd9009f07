"""
AI-powered job evaluation service using OpenRouter API
Provides intelligent analysis and insights for job descriptions
"""

import json
import requests
from typing import Dict, Any, List
import os

class JobEvaluationService:
    def __init__(self):
        self.api_key = os.getenv('OPENROUTER_API_KEY', '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')
        self.base_url = 'https://openrouter.ai/api/v1'
        self.model = 'google/gemini-2.0-flash-exp:free'

    def evaluate_job(self, job_title: str, company: str, description: str, location: str = '') -> Dict[str, Any]:
        """
        Evaluate a job description using AI
        
        Args:
            job_title: Job position title
            company: Company name
            description: Job description text
            location: Job location
            
        Returns:
            Dict containing evaluation results
        """
        try:
            prompt = self._create_evaluation_prompt(job_title, company, description, location)
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://hireista.app',
                'X-Title': 'Hireista Job Platform'
            }
            
            payload = {
                'model': self.model,
                'messages': [
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'temperature': 0.7,
                'max_tokens': 1000
            }
            
            response = requests.post(
                f'{self.base_url}/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                if ai_response:
                    return self._parse_evaluation_response(ai_response)
            
            print(f"OpenRouter API error: {response.status_code}")
            
        except Exception as e:
            print(f"Error evaluating job with AI: {e}")
            
        return self._get_fallback_evaluation(job_title, company)

    def _create_evaluation_prompt(self, job_title: str, company: str, description: str, location: str) -> str:
        """Create a comprehensive evaluation prompt"""
        return f"""Analyze this job posting and provide a comprehensive evaluation. Respond with a JSON object containing the following fields:

Job Details:
- Title: {job_title}
- Company: {company}
- Location: {location}
- Description: {description}

Please analyze and respond with this exact JSON structure:
{{
  "overallScore": <number 1-10>,
  "strengths": ["strength1", "strength2", "strength3"],
  "concerns": ["concern1", "concern2"],
  "salaryEstimate": "estimated salary range",
  "careerGrowth": "brief assessment of growth potential",
  "workLifeBalance": "assessment of work-life balance",
  "companyReputation": "brief company reputation assessment",
  "skillsRequired": ["skill1", "skill2", "skill3"],
  "aiSummary": "2-3 sentence summary of why this job is worth considering"
}}

Focus on:
1. Overall job quality and appeal (1-10 score)
2. Key strengths and opportunities
3. Potential red flags or concerns
4. Realistic salary expectations for this role
5. Career growth potential
6. Work-life balance indicators
7. Company reputation and stability
8. Essential skills needed
9. A compelling summary for job seekers

Be realistic, helpful, and provide actionable insights."""

    def _parse_evaluation_response(self, response: str) -> Dict[str, Any]:
        """Parse the AI response into structured data"""
        try:
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                parsed = json.loads(json_match.group())
                
                return {
                    'overallScore': max(1, min(10, parsed.get('overallScore', 7))),
                    'strengths': parsed.get('strengths', ['Competitive opportunity', 'Professional growth', 'Skill development']),
                    'concerns': parsed.get('concerns', ['Limited information available']),
                    'salaryEstimate': parsed.get('salaryEstimate', 'Competitive salary'),
                    'careerGrowth': parsed.get('careerGrowth', 'Good growth potential'),
                    'workLifeBalance': parsed.get('workLifeBalance', 'Standard work-life balance'),
                    'companyReputation': parsed.get('companyReputation', 'Established company'),
                    'skillsRequired': parsed.get('skillsRequired', ['Professional experience', 'Communication skills', 'Technical aptitude']),
                    'aiSummary': parsed.get('aiSummary', 'This position offers a solid opportunity for professional growth with competitive compensation.')
                }
        except Exception as e:
            print(f"Failed to parse AI evaluation response: {e}")
        
        # Fallback to extracting information from free text
        return self._extract_from_free_text(response)

    def _extract_from_free_text(self, response: str) -> Dict[str, Any]:
        """Extract evaluation data from free text response"""
        import re
        
        score_match = re.search(r'score[:\s]*(\d+)', response, re.IGNORECASE)
        score = int(score_match.group(1)) if score_match else 7
        
        return {
            'overallScore': max(1, min(10, score)),
            'strengths': ['Professional opportunity', 'Career development', 'Competitive role'],
            'concerns': ['Standard considerations'],
            'salaryEstimate': 'Competitive compensation',
            'careerGrowth': 'Good advancement opportunities',
            'workLifeBalance': 'Standard work-life balance',
            'companyReputation': 'Reputable organization',
            'skillsRequired': ['Professional experience', 'Strong communication', 'Technical skills'],
            'aiSummary': response[:200] + '...' if len(response) > 200 else 'This position offers excellent opportunities for professional growth and development.'
        }

    def _get_fallback_evaluation(self, job_title: str, company: str) -> Dict[str, Any]:
        """Provide fallback evaluation when AI fails"""
        return {
            'overallScore': 7,
            'strengths': [
                'Opportunity to work with a established company',
                'Professional development potential',
                'Competitive role in the market'
            ],
            'concerns': [
                'Limited specific details available',
                'Standard employment considerations'
            ],
            'salaryEstimate': 'Competitive salary based on experience',
            'careerGrowth': 'Good potential for advancement',
            'workLifeBalance': 'Standard work-life balance expected',
            'companyReputation': f'{company} is an established organization',
            'skillsRequired': [
                'Relevant professional experience',
                'Strong communication skills',
                'Industry knowledge'
            ],
            'aiSummary': f'This {job_title} position at {company} offers a solid opportunity for professional growth with competitive compensation and benefits.'
        }

    def format_evaluation_for_display(self, evaluation: Dict[str, Any]) -> str:
        """Format evaluation for display in job descriptions"""
        score = evaluation.get('overallScore', 7)
        score_emoji = '🌟' if score >= 8 else '⭐' if score >= 6 else '✨'
        
        strengths = evaluation.get('strengths', [])
        concerns = evaluation.get('concerns', [])
        skills = evaluation.get('skillsRequired', [])
        
        return f"""
**AI Job Evaluation {score_emoji} ({score}/10)**

{evaluation.get('aiSummary', 'This position offers professional growth opportunities.')}

**💰 Estimated Salary:** {evaluation.get('salaryEstimate', 'Competitive')}

**📈 Career Growth:** {evaluation.get('careerGrowth', 'Good potential')}

**⚖️ Work-Life Balance:** {evaluation.get('workLifeBalance', 'Standard balance')}

**🏢 Company Reputation:** {evaluation.get('companyReputation', 'Established company')}

**✅ Key Strengths:**
{chr(10).join([f'• {strength}' for strength in strengths])}

**⚠️ Considerations:**
{chr(10).join([f'• {concern}' for concern in concerns])}

**🎯 Skills Needed:**
{chr(10).join([f'• {skill}' for skill in skills])}
        """.strip()

# Global service instance
job_evaluation_service = JobEvaluationService()
