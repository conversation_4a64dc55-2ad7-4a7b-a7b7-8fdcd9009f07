import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { supabase } from '@/lib/supabase';
import FontAwesome from '@expo/vector-icons/FontAwesome';

export default function JobPreferencesModal() {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);
  
  // Job type options
  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'];
  const experienceLevels = ['entry', 'junior', 'mid', 'senior', 'lead', 'executive'];
  const industries = [
    'Technology', 'Healthcare', 'Finance', 'Education', 'Marketing', 'Sales',
    'Design', 'Engineering', 'Data Science', 'Product Management', 'Human Resources',
    'Operations', 'Customer Service', 'Consulting', 'Media', 'Non-profit', 'Government', 'Other'
  ];
  const companySizes = ['startup', 'small', 'medium', 'large', 'enterprise'];
  const remotePreferences = ['required', 'preferred', 'acceptable', 'not_preferred'];
  
  // Form state
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>(['Full-time']);
  const [experienceLevel, setExperienceLevel] = useState('mid');
  const [selectedIndustries, setSelectedIndustries] = useState<string[]>([]);
  const [selectedCompanySizes, setSelectedCompanySizes] = useState<string[]>([]);
  const [remoteWorkPreference, setRemoteWorkPreference] = useState('preferred');
  const [preferredLocations, setPreferredLocations] = useState('');
  const [willingToRelocate, setWillingToRelocate] = useState(false);
  const [salaryMin, setSalaryMin] = useState('');
  const [salaryMax, setSalaryMax] = useState('');
  const [preferredJobTitles, setPreferredJobTitles] = useState('');
  const [requiredSkills, setRequiredSkills] = useState('');
  const [preferredSkills, setPreferredSkills] = useState('');
  
  // Load existing preferences
  useEffect(() => {
    loadPreferences();
  }, []);
  
  const loadPreferences = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('user_job_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }
      
      if (data) {
        setSelectedJobTypes(data.preferred_job_types || ['Full-time']);
        setExperienceLevel(data.experience_level || 'mid');
        setSelectedIndustries(data.preferred_industries || []);
        setSelectedCompanySizes(data.preferred_company_sizes || []);
        setRemoteWorkPreference(data.remote_work_preference || 'preferred');
        setPreferredLocations((data.preferred_locations || []).join(', '));
        setWillingToRelocate(data.willing_to_relocate || false);
        setSalaryMin(data.min_salary ? data.min_salary.toString() : '');
        setSalaryMax(data.max_salary ? data.max_salary.toString() : '');
        setPreferredJobTitles((data.preferred_job_titles || []).join(', '));
        setRequiredSkills((data.required_skills || []).join(', '));
        setPreferredSkills((data.preferred_skills || []).join(', '));
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      setError('Failed to load preferences');
    } finally {
      setLoading(false);
    }
  };
  
  // Toggle selection in array
  const toggleSelection = (item: string, currentSelections: string[], setSelections: React.Dispatch<React.SetStateAction<string[]>>, maxSelections = 5) => {
    if (currentSelections.includes(item)) {
      setSelections(currentSelections.filter(i => i !== item));
    } else {
      if (currentSelections.length < maxSelections) {
        setSelections([...currentSelections, item]);
      } else {
        Alert.alert('Selection limit', `You can only select up to ${maxSelections} options.`);
      }
    }
  };
  
  // Save preferences
  const savePreferences = async () => {
    if (!user) {
      setError('User not authenticated');
      return;
    }
    
    setSaving(true);
    setError('');
    
    try {
      // Parse comma-separated values
      const locationsArray = preferredLocations.split(',').map(loc => loc.trim()).filter(loc => loc.length > 0);
      const jobTitlesArray = preferredJobTitles.split(',').map(title => title.trim()).filter(title => title.length > 0);
      const requiredSkillsArray = requiredSkills.split(',').map(skill => skill.trim()).filter(skill => skill.length > 0);
      const preferredSkillsArray = preferredSkills.split(',').map(skill => skill.trim()).filter(skill => skill.length > 0);
      
      const { error } = await supabase
        .from('user_job_preferences')
        .upsert({
          user_id: user.id,
          preferred_job_types: selectedJobTypes,
          experience_level: experienceLevel,
          preferred_industries: selectedIndustries,
          preferred_company_sizes: selectedCompanySizes,
          remote_work_preference: remoteWorkPreference,
          preferred_locations: locationsArray,
          willing_to_relocate: willingToRelocate,
          min_salary: salaryMin ? parseInt(salaryMin, 10) : null,
          max_salary: salaryMax ? parseInt(salaryMax, 10) : null,
          preferred_job_titles: jobTitlesArray,
          required_skills: requiredSkillsArray,
          preferred_skills: preferredSkillsArray
        });
      
      if (error) throw error;
      
      Alert.alert('Success', 'Your job preferences have been updated!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
      
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError('Failed to save preferences. Please try again.');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: themeColors.text }]}>Loading preferences...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar barStyle={theme === 'dark' ? 'light-content' : 'dark-content'} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <FontAwesome name="times" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>Job Preferences</Text>
        <TouchableOpacity onPress={savePreferences} disabled={saving}>
          <Text style={[styles.saveButton, { color: saving ? themeColors.textSecondary : themeColors.primary }]}>
            {saving ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {error ? (
          <Text style={styles.error}>{error}</Text>
        ) : null}
        
        {/* Job Types Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Job Types</Text>
          <View style={styles.optionsGrid}>
            {jobTypes.map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.optionButton,
                  selectedJobTypes.includes(type) && { backgroundColor: themeColors.primary },
                  { borderColor: themeColors.border }
                ]}
                onPress={() => toggleSelection(type, selectedJobTypes, setSelectedJobTypes)}
              >
                <Text
                  style={[
                    styles.optionText,
                    selectedJobTypes.includes(type) && { color: '#fff' },
                    !selectedJobTypes.includes(type) && { color: themeColors.text }
                  ]}
                >
                  {type}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Experience Level Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Experience Level</Text>
          <View style={styles.optionsRow}>
            {experienceLevels.map((level) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.pillButton,
                  experienceLevel === level && { backgroundColor: themeColors.primary },
                  { borderColor: themeColors.border }
                ]}
                onPress={() => setExperienceLevel(level)}
              >
                <Text
                  style={[
                    styles.pillText,
                    experienceLevel === level && { color: '#fff' },
                    experienceLevel !== level && { color: themeColors.text }
                  ]}
                >
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Industries Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Industries</Text>
          <View style={styles.optionsGrid}>
            {industries.map((industry) => (
              <TouchableOpacity
                key={industry}
                style={[
                  styles.optionButton,
                  selectedIndustries.includes(industry) && { backgroundColor: themeColors.primary },
                  { borderColor: themeColors.border }
                ]}
                onPress={() => toggleSelection(industry, selectedIndustries, setSelectedIndustries)}
              >
                <Text
                  style={[
                    styles.optionText,
                    selectedIndustries.includes(industry) && { color: '#fff' },
                    !selectedIndustries.includes(industry) && { color: themeColors.text }
                  ]}
                >
                  {industry}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Company Size Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Company Sizes</Text>
          <View style={styles.optionsRow}>
            {companySizes.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.pillButton,
                  selectedCompanySizes.includes(size) && { backgroundColor: themeColors.primary },
                  { borderColor: themeColors.border }
                ]}
                onPress={() => toggleSelection(size, selectedCompanySizes, setSelectedCompanySizes)}
              >
                <Text
                  style={[
                    styles.pillText,
                    selectedCompanySizes.includes(size) && { color: '#fff' },
                    !selectedCompanySizes.includes(size) && { color: themeColors.text }
                  ]}
                >
                  {size.charAt(0).toUpperCase() + size.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Remote Work Preference */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Remote Work Preference</Text>
          <View style={styles.optionsRow}>
            {remotePreferences.map((pref) => (
              <TouchableOpacity
                key={pref}
                style={[
                  styles.pillButton,
                  remoteWorkPreference === pref && { backgroundColor: themeColors.primary },
                  { borderColor: themeColors.border }
                ]}
                onPress={() => setRemoteWorkPreference(pref)}
              >
                <Text
                  style={[
                    styles.pillText,
                    remoteWorkPreference === pref && { color: '#fff' },
                    remoteWorkPreference !== pref && { color: themeColors.text }
                  ]}
                >
                  {pref.charAt(0).toUpperCase() + pref.slice(1).replace('_', ' ')}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Location Preferences */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Preferred Locations</Text>
          <TextInput
            style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
            value={preferredLocations}
            onChangeText={setPreferredLocations}
            placeholder="e.g., San Francisco, New York, Remote"
            placeholderTextColor={themeColors.textSecondary}
            multiline
          />

          <View style={styles.switchContainer}>
            <Text style={[styles.switchLabel, { color: themeColors.text }]}>Willing to relocate</Text>
            <Switch
              value={willingToRelocate}
              onValueChange={setWillingToRelocate}
              trackColor={{ false: themeColors.border, true: themeColors.primary }}
              thumbColor={willingToRelocate ? '#fff' : themeColors.textSecondary}
            />
          </View>
        </View>

        {/* Salary Range */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Salary Range (USD)</Text>
          <View style={styles.salaryContainer}>
            <View style={styles.salaryField}>
              <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>Minimum</Text>
              <TextInput
                style={[styles.salaryInput, { borderColor: themeColors.border, color: themeColors.text }]}
                keyboardType="numeric"
                value={salaryMin}
                onChangeText={setSalaryMin}
                placeholder="50000"
                placeholderTextColor={themeColors.textSecondary}
              />
            </View>

            <View style={styles.salaryField}>
              <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>Maximum</Text>
              <TextInput
                style={[styles.salaryInput, { borderColor: themeColors.border, color: themeColors.text }]}
                keyboardType="numeric"
                value={salaryMax}
                onChangeText={setSalaryMax}
                placeholder="150000"
                placeholderTextColor={themeColors.textSecondary}
              />
            </View>
          </View>
        </View>

        {/* Job Titles */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Preferred Job Titles</Text>
          <TextInput
            style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
            value={preferredJobTitles}
            onChangeText={setPreferredJobTitles}
            placeholder="e.g., Software Engineer, Frontend Developer"
            placeholderTextColor={themeColors.textSecondary}
            multiline
          />
        </View>

        {/* Required Skills */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Required Skills</Text>
          <TextInput
            style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
            value={requiredSkills}
            onChangeText={setRequiredSkills}
            placeholder="e.g., JavaScript, React, Node.js"
            placeholderTextColor={themeColors.textSecondary}
            multiline
          />
        </View>

        {/* Preferred Skills */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Preferred Skills</Text>
          <TextInput
            style={[styles.textInput, { borderColor: themeColors.border, color: themeColors.text }]}
            value={preferredSkills}
            onChangeText={setPreferredSkills}
            placeholder="e.g., TypeScript, GraphQL, AWS"
            placeholderTextColor={themeColors.textSecondary}
            multiline
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  error: {
    color: 'red',
    textAlign: 'center',
    margin: 16,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  optionButton: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
    margin: 4,
    minWidth: '30%',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  pillButton: {
    borderWidth: 1,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    margin: 4,
  },
  pillText: {
    fontSize: 14,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 50,
    textAlignVertical: 'top',
    marginTop: 8,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  salaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  salaryField: {
    flex: 1,
    marginHorizontal: 4,
  },
  salaryLabel: {
    fontSize: 14,
    marginBottom: 8,
    fontWeight: '500',
  },
  salaryInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
});
